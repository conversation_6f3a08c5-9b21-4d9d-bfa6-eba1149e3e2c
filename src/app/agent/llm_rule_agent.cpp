#include "llm_rule_agent.h"

#include <QBuffer>
#include <QFile>
#include <QImage>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMutexLocker>

#include <QMessageBox>

namespace Agent {
const QString Task = "task";
const QString Image = "base64";
const QString ComponentList = "netlist";
const QString NearRule = "PROXIMITY";
const QString TextRule = "text_to_analysis";

LLMRuleAgent::LLMRuleAgent() {
  if (!mq_client_.ConnectReq("tcp://10.0.20.173:5555")) {
    QMessageBox::warning(nullptr, "Warning",
                         mq_client_.LastErrorMessage().data());
  }
}

LLMRuleAgent::~LLMRuleAgent() {
  mq_client_.DisconnectReq();
}

QJsonObject LLMRuleAgent::GetNearRule(const QStringList& comp_list) {
  QMutexLocker locker(&mutex_);
  QString buf;
  BuildMqRequest(NearRule, sch_image_path_, comp_list, buf);
  std::string reply;
  if (mq_client_.Request(buf.toStdString().c_str(), buf.size(), reply)) {
    return QJsonDocument::fromJson(QByteArray(reply.data(), reply.size())).
        object();
  }
  return {};
}

void LLMRuleAgent::SetSchImagePath(const QString& image_path) {
  sch_image_path_ = image_path;
}

bool LLMRuleAgent::CheckSchImagePath() {
  if (sch_image_path_.isEmpty()) {
    return false;
  }
  if (!QFile::exists(sch_image_path_)) {
    return false;
  }
  return true;
}

void LLMRuleAgent::BuildMqRequest(const QString& opt, const QString& image_path,
                                  const QStringList& comp_list, QString& buf) {
  QJsonObject root;
  root[Task] = opt;

  QString base64;
  ImageToBase64(image_path, base64);
  root[Image] = base64;

  QJsonArray comp_array;
  for (auto& comp_name : comp_list) {
    comp_array.append(comp_name);
  }
  root[ComponentList] = std::move(comp_array);

  QJsonDocument doc(root);
  buf = doc.toJson(QJsonDocument::Compact);
}

void LLMRuleAgent::ImageToBase64(const QString& image_path, QString& base64) {
  QImage image(image_path);
  QByteArray ba;
  QBuffer buffer(&ba);
  buffer.open(QIODevice::WriteOnly);
  image.save(&buffer, "PNG");
  base64 = ba.toBase64();
}

QJsonObject LLMRuleAgent::TextToRule(const QString& text) {
  QMutexLocker locker(&mutex_);
  QJsonObject root;
  root[Task] = TextRule;
  root[text] = text;

  QJsonDocument doc(root);
  auto buf = doc.toJson(QJsonDocument::Compact);

  std::string reply;
  if (mq_client_.Request(buf.toStdString().c_str(), buf.size(), reply)) {
    return QJsonDocument::fromJson(QByteArray(reply.data(), reply.size())).
        object();
  }
  return {};
}
} // namespace Agent